'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Smartphone,
  MapPin,
  Star,
  Calendar,
  Package,
  ClipboardCheck,
  Wrench,
  ShieldCheck,
  User,
  Building,
  Barcode,
  Activity
} from 'lucide-react';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';

interface DeviceDetailsSectionProps {
  device: any;
  searchedImei: string;
  customerViewDetails: any;
  evaluationOrders: any[];
  supplyOrders: any[];
  suppliers: any[];
  warehouseTransfers: any[];
}

// دالة مساعدة لتنسيق التاريخ بالعربية
function formatArabicDate(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('ar-EG', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

// دالة للحصول على لون الحالة
function getStatusColor(status: string): string {
  switch (status) {
    case 'متاح':
      return 'bg-green-100 text-green-800 border-green-200';
    case 'مباع':
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'قيد الإصلاح':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'معطل':
      return 'bg-red-100 text-red-800 border-red-200';
    case 'مرتجع':
      return 'bg-orange-100 text-orange-800 border-orange-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
}

// دالة للحصول على أيقونة الحالة
function getStatusIcon(status: string) {
  switch (status) {
    case 'متاح':
      return <Package className="h-4 w-4" />;
    case 'مباع':
      return <User className="h-4 w-4" />;
    case 'قيد الإصلاح':
      return <Wrench className="h-4 w-4" />;
    case 'معطل':
      return <Activity className="h-4 w-4" />;
    case 'مرتجع':
      return <Package className="h-4 w-4" />;
    default:
      return <Package className="h-4 w-4" />;
  }
}

export default function DeviceDetailsSection({
  device,
  searchedImei,
  customerViewDetails,
  evaluationOrders,
  supplyOrders,
  suppliers,
  warehouseTransfers
}: DeviceDetailsSectionProps) {
  
  // الحصول على آخر تقييم للجهاز
  const latestEvaluation = evaluationOrders
    .filter(order => Array.isArray(order.items) && order.items.some(item => item.deviceId === searchedImei))
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0];
  
  const evaluatedItem = latestEvaluation?.items?.find(item => item.deviceId === searchedImei);

  // الحصول على معلومات التوريد
  const supplyOrder = supplyOrders.find(so => 
    Array.isArray(so.items) && so.items.some(item => item.imei === searchedImei)
  );
  const supplier = supplyOrder ? suppliers.find(s => s.id === supplyOrder.supplierId) : null;

  // الحصول على آخر موقع للجهاز
  const getDeviceLocation = () => {
    // البحث في التحويلات المخزنية
    const latestTransfer = warehouseTransfers
      .filter(transfer => Array.isArray(transfer.items) && transfer.items.some(item => item.deviceId === searchedImei))
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0];
    
    if (latestTransfer && latestTransfer.status === 'completed') {
      return latestTransfer.toWarehouseName;
    }
    
    // إذا لم يكن هناك تحويل، استخدم مخزن التوريد
    if (supplyOrder && supplyOrder.warehouseId) {
      return `مخزن رقم ${supplyOrder.warehouseId}`;
    }
    
    return 'غير محدد';
  };

  return (
    <Card className="device-details-card border-0 shadow-lg bg-gradient-to-br from-slate-50 to-gray-50 dark:from-slate-900/20 dark:to-gray-900/20">
      <CardHeader className="pb-4">
        <div className="flex items-center gap-3">
          <div className="p-3 bg-blue-100 rounded-xl">
            <Smartphone className="h-8 w-8 text-blue-600" />
          </div>
          <div>
            <CardTitle className="text-2xl font-bold text-gray-800">
              تفاصيل الجهاز
            </CardTitle>
            <CardDescription className="text-gray-600">
              معلومات شاملة عن الجهاز وحالته الحالية
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          
          {/* المعلومات الأساسية */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-800 border-b pb-2">المعلومات الأساسية</h3>
            
            <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
              <div className="flex items-center gap-2 mb-2">
                <Smartphone className="h-4 w-4 text-blue-600" />
                <span className="font-semibold text-gray-700">الموديل</span>
              </div>
              <p className="text-gray-900 font-medium text-lg">{device.model}</p>
            </div>

            <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
              <div className="flex items-center gap-2 mb-2">
                <Barcode className="h-4 w-4 text-gray-600" />
                <span className="font-semibold text-gray-700">الرقم التسلسلي</span>
              </div>
              <p className="text-gray-900 font-mono text-sm break-all">{searchedImei}</p>
            </div>

            <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
              <div className="flex items-center gap-2 mb-2">
                <Activity className="h-4 w-4 text-green-600" />
                <span className="font-semibold text-gray-700">الحالة الحالية</span>
              </div>
              <Badge className={`${getStatusColor(device.status)} border px-3 py-1`}>
                {getStatusIcon(device.status)}
                <span className="mr-1">{device.status}</span>
              </Badge>
            </div>
          </div>

          {/* معلومات التقييم */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-800 border-b pb-2">التقييم والجودة</h3>
            
            {evaluatedItem ? (
              <>
                <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                  <div className="flex items-center gap-2 mb-2">
                    <Star className="h-4 w-4 text-yellow-600" />
                    <span className="font-semibold text-gray-700">التقييم النهائي</span>
                  </div>
                  <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200 px-3 py-1">
                    {evaluatedItem.finalGrade}
                  </Badge>
                </div>

                <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                  <div className="flex items-center gap-2 mb-3">
                    <ClipboardCheck className="h-4 w-4 text-indigo-600" />
                    <span className="font-semibold text-gray-700">تفاصيل التقييم</span>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">الخارجي:</span>
                      <Badge variant="outline" className="text-xs">{evaluatedItem.externalGrade}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">الشاشة:</span>
                      <Badge variant="outline" className="text-xs">{evaluatedItem.screenGrade}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">الشبكة:</span>
                      <Badge variant="outline" className="text-xs">{evaluatedItem.networkGrade}</Badge>
                    </div>
                  </div>
                </div>

                {(evaluatedItem.fault || evaluatedItem.damageType) && (
                  <div className="bg-red-50 p-4 rounded-lg border border-red-200 shadow-sm">
                    <div className="flex items-center gap-2 mb-2">
                      <Activity className="h-4 w-4 text-red-600" />
                      <span className="font-semibold text-red-700">الأعطال المكتشفة</span>
                    </div>
                    <p className="text-red-800 text-sm">
                      {evaluatedItem.fault || evaluatedItem.damageType}
                    </p>
                  </div>
                )}

                <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                  <div className="flex items-center gap-2 mb-2">
                    <Calendar className="h-4 w-4 text-gray-600" />
                    <span className="font-semibold text-gray-700">تاريخ التقييم</span>
                  </div>
                  <p className="text-gray-900 text-sm">{formatArabicDate(latestEvaluation.date)}</p>
                </div>
              </>
            ) : (
              <div className="bg-gray-50 p-6 rounded-lg border border-gray-200 text-center">
                <ClipboardCheck className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500">لم يتم تقييم هذا الجهاز بعد</p>
              </div>
            )}
          </div>

          {/* معلومات الموقع والتوريد */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-800 border-b pb-2">الموقع والتوريد</h3>
            
            <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
              <div className="flex items-center gap-2 mb-2">
                <MapPin className="h-4 w-4 text-green-600" />
                <span className="font-semibold text-gray-700">الموقع الحالي</span>
              </div>
              <p className="text-gray-900 font-medium">{getDeviceLocation()}</p>
            </div>

            {supplier && (
              <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                <div className="flex items-center gap-2 mb-2">
                  <Building className="h-4 w-4 text-purple-600" />
                  <span className="font-semibold text-gray-700">المورد</span>
                </div>
                <p className="text-gray-900 font-medium">{supplier.name}</p>
              </div>
            )}

            {supplyOrder && (
              <>
                <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                  <div className="flex items-center gap-2 mb-2">
                    <Package className="h-4 w-4 text-cyan-600" />
                    <span className="font-semibold text-gray-700">أمر التوريد</span>
                  </div>
                  <p className="text-gray-900 font-medium">{supplyOrder.supplyOrderId}</p>
                </div>

                <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                  <div className="flex items-center gap-2 mb-2">
                    <Calendar className="h-4 w-4 text-gray-600" />
                    <span className="font-semibold text-gray-700">تاريخ التوريد</span>
                  </div>
                  <p className="text-gray-900 text-sm">{formatArabicDate(supplyOrder.supplyDate)}</p>
                </div>
              </>
            )}

            {/* معلومات الضمان للعرض العام */}
            {customerViewDetails?.warrantyInfo && (
              <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                <div className="flex items-center gap-2 mb-2">
                  <ShieldCheck className="h-4 w-4 text-blue-600" />
                  <span className="font-semibold text-gray-700">حالة الضمان</span>
                </div>
                <Badge className={`${
                  customerViewDetails.warrantyInfo.status === 'في الضمان' ? 'bg-green-100 text-green-800 border-green-200' :
                  customerViewDetails.warrantyInfo.status === 'ضمان منتهي' ? 'bg-red-100 text-red-800 border-red-200' : 
                  'bg-gray-100 text-gray-800 border-gray-200'
                } border px-3 py-1`}>
                  {customerViewDetails.warrantyInfo.status}
                </Badge>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
